from run import EMOE_run
from mamba_modal.DEMO2.demo_run import DEMO_run

if __name__ == '__main__':
    EMOE_run(model_name='emoe', dataset_name='mosi', is_tune=False, seeds=[1111], model_save_dir="./pt", res_save_dir="./result", log_dir="./log", mode='train')
    #DEMO_run(model_name='demo', dataset_name='mosi', is_tune=False, seeds=[1111], model_save_dir="./pt", res_save_dir="./result", log_dir="./log", mode='train')
